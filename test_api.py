#!/usr/bin/env python3
"""
Test script for OpenVoice API

This script demonstrates how to use the OpenVoice API endpoints.
"""

import requests
import json
import os
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_status():
    """Test the status endpoint"""
    print("\nTesting status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Status check: {response.status_code}")
        data = response.json()
        print(f"Status: {data['status']}")
        print(f"Device: {data['device']}")
        print(f"Available speakers: {data['available_speakers']}")
        return response.status_code == 200
    except Exception as e:
        print(f"Status check failed: {e}")
        return False

def test_tts(text="Hello, this is a test of OpenVoice text-to-speech!", output_file="test_tts.wav"):
    """Test text-to-speech endpoint"""
    print(f"\nTesting TTS with text: '{text}'")
    try:
        data = {
            "text": text,
            "speaker": "default",
            "language": "English",
            "speed": 1.0
        }
        
        response = requests.post(f"{BASE_URL}/tts", json=data)
        print(f"TTS status: {response.status_code}")
        
        if response.status_code == 200:
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"Audio saved to: {output_file}")
            return True
        else:
            print(f"TTS failed: {response.text}")
            return False
    except Exception as e:
        print(f"TTS test failed: {e}")
        return False

def test_speaker_embedding(audio_file_path):
    """Test speaker embedding extraction"""
    print(f"\nTesting speaker embedding extraction with: {audio_file_path}")
    
    if not os.path.exists(audio_file_path):
        print(f"Audio file not found: {audio_file_path}")
        return False, None
    
    try:
        with open(audio_file_path, "rb") as f:
            files = {"audio_file": (os.path.basename(audio_file_path), f, "audio/wav")}
            response = requests.post(f"{BASE_URL}/extract_speaker_embedding", files=files)
        
        print(f"Speaker embedding status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Embedding extracted: {data['embedding_id']}")
            return True, data['embedding_id']
        else:
            print(f"Speaker embedding failed: {response.text}")
            return False, None
    except Exception as e:
        print(f"Speaker embedding test failed: {e}")
        return False, None

def test_voice_clone(text, reference_audio_path, output_file="test_voice_clone.wav"):
    """Test voice cloning endpoint"""
    print(f"\nTesting voice cloning with text: '{text}'")
    print(f"Reference audio: {reference_audio_path}")
    
    if not os.path.exists(reference_audio_path):
        print(f"Reference audio file not found: {reference_audio_path}")
        return False
    
    try:
        data = {
            "text": text,
            "language": "English",
            "speed": 1.0,
            "tau": 0.3
        }
        
        with open(reference_audio_path, "rb") as f:
            files = {"reference_audio": (os.path.basename(reference_audio_path), f, "audio/wav")}
            response = requests.post(f"{BASE_URL}/voice_clone", data=data, files=files)
        
        print(f"Voice cloning status: {response.status_code}")
        
        if response.status_code == 200:
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"Cloned voice audio saved to: {output_file}")
            return True
        else:
            print(f"Voice cloning failed: {response.text}")
            return False
    except Exception as e:
        print(f"Voice cloning test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("OpenVoice API Test Suite")
    print("=" * 40)
    
    # Test basic connectivity
    if not test_health_check():
        print("❌ API server is not running or not accessible")
        print("Please start the server with: python openvoice_api_server.py")
        return
    
    if not test_status():
        print("❌ Status endpoint failed")
        return
    
    print("✅ API server is running")
    
    # Test TTS
    if test_tts():
        print("✅ TTS test passed")
    else:
        print("❌ TTS test failed")
    
    # Test with different text and Chinese if available
    test_tts("This is another test with different text.", "test_tts_2.wav")
    
    # Look for reference audio files
    reference_files = []
    possible_refs = [
        "resources/demo_speaker0.mp3",
        "resources/demo_speaker1.mp3", 
        "resources/demo_speaker2.mp3",
        "resources/example_reference.mp3",
        "test_tts.wav"  # Use our generated TTS as reference
    ]
    
    for ref_file in possible_refs:
        if os.path.exists(ref_file):
            reference_files.append(ref_file)
    
    if reference_files:
        print(f"\nFound reference audio files: {reference_files}")
        
        # Test speaker embedding extraction
        success, embedding_id = test_speaker_embedding(reference_files[0])
        if success:
            print("✅ Speaker embedding extraction passed")
        else:
            print("❌ Speaker embedding extraction failed")
        
        # Test voice cloning
        clone_text = "This is a test of voice cloning using OpenVoice API."
        if test_voice_clone(clone_text, reference_files[0]):
            print("✅ Voice cloning test passed")
        else:
            print("❌ Voice cloning test failed")
    else:
        print("\n⚠️  No reference audio files found for voice cloning tests")
        print("Available files to test with:")
        for ref_file in possible_refs:
            print(f"  - {ref_file}")
    
    print("\n" + "=" * 40)
    print("Test suite completed!")
    print("\nGenerated files:")
    for file in ["test_tts.wav", "test_tts_2.wav", "test_voice_clone.wav"]:
        if os.path.exists(file):
            print(f"  - {file}")

if __name__ == "__main__":
    main()
