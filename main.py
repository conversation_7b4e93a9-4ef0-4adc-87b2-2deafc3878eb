#!/usr/bin/env python3
"""
OpenVoice API Server

A FastAPI-based REST API server for OpenVoice text-to-speech and voice cloning functionality.
"""

import os
import io
import uuid
import torch
import tempfile
import traceback
import time
import zipfile
import urllib.request
from contextlib import asynccontextmanager
from typing import List
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# OpenVoice imports
from openvoice.api import BaseSpeakerTTS, ToneColorConverter

# Global variables for models
models = {}
device = None
output_dir = "api_outputs"

def download_and_extract_models():
    """Download and extract OpenVoice model checkpoints if not present"""
    checkpoint_zip = "checkpoints_1226.zip"
    checkpoint_url = "https://myshell-public-repo-host.s3.amazonaws.com/openvoice/checkpoints_1226.zip"

    # Check if models already exist
    required_paths = [
        'checkpoints/base_speakers/EN/config.json',
        'checkpoints/base_speakers/EN/checkpoint.pth',
        'checkpoints/converter/config.json',
        'checkpoints/converter/checkpoint.pth'
    ]

    if all(os.path.exists(path) for path in required_paths):
        print("✅ Model checkpoints already exist")
        return True

    print("📥 Downloading OpenVoice model checkpoints...")

    try:
        # Download the checkpoint file if it doesn't exist
        if not os.path.exists(checkpoint_zip):
            print(f"Downloading {checkpoint_url}...")
            urllib.request.urlretrieve(checkpoint_url, checkpoint_zip)
            print("✅ Download completed")
        else:
            print("✅ Checkpoint zip already exists")

        # Extract the checkpoint file
        print("📂 Extracting model checkpoints...")
        with zipfile.ZipFile(checkpoint_zip, 'r') as zip_ref:
            zip_ref.extractall('.')

        print("✅ Model checkpoints extracted successfully")

        # Verify extraction
        if all(os.path.exists(path) for path in required_paths):
            print("✅ All required model files are present")
            return True
        else:
            print("❌ Some model files are missing after extraction")
            return False

    except Exception as e:
        print(f"❌ Error downloading/extracting models: {e}")
        return False

@asynccontextmanager
async def lifespan(_app: FastAPI):
    # Startup
    global models, device, output_dir

    print("Starting OpenVoice API Server...")

    # Set device
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Download and extract models if needed
    models_ready = download_and_extract_models()

    if models_ready:
        try:
            # Load English base speaker
            en_ckpt_base = 'checkpoints/base_speakers/EN'
            models['en_base_speaker_tts'] = BaseSpeakerTTS(f'{en_ckpt_base}/config.json', device=device)
            models['en_base_speaker_tts'].load_ckpt(f'{en_ckpt_base}/checkpoint.pth')

            # Load tone color converter
            ckpt_converter = 'checkpoints/converter'
            models['tone_color_converter'] = ToneColorConverter(f'{ckpt_converter}/config.json', device=device)
            models['tone_color_converter'].load_ckpt(f'{ckpt_converter}/checkpoint.pth')

            # Load default speaker embeddings
            models['en_source_default_se'] = torch.load(f'{en_ckpt_base}/en_default_se.pth').to(device)
            models['en_source_style_se'] = torch.load(f'{en_ckpt_base}/en_style_se.pth').to(device)

            # Try to load Chinese models if available
            zh_ckpt_base = 'checkpoints/base_speakers/ZH'
            if os.path.exists(zh_ckpt_base):
                models['zh_base_speaker_tts'] = BaseSpeakerTTS(f'{zh_ckpt_base}/config.json', device=device)
                models['zh_base_speaker_tts'].load_ckpt(f'{zh_ckpt_base}/checkpoint.pth')
                models['zh_source_se'] = torch.load(f'{zh_ckpt_base}/zh_default_se.pth').to(device)

            print("Models loaded successfully!")

        except Exception as e:
            print(f"Error loading models: {e}")
            print("API will run in limited mode")
    else:
        print("Model checkpoints not found. Please download them first.")
        print("See docs/USAGE.md for instructions")

    yield

    # Shutdown
    print("Shutting down OpenVoice API Server...")

# Initialize FastAPI app
app = FastAPI(
    title="OpenVoice API",
    description="REST API for OpenVoice text-to-speech and voice cloning",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class TTSRequest(BaseModel):
    text: str
    speaker: str = "default"
    language: str = "English"
    speed: float = 1.0

class VoiceCloneRequest(BaseModel):
    text: str
    language: str = "English"
    speed: float = 1.0
    tau: float = 0.3

class StatusResponse(BaseModel):
    status: str
    message: str
    device: str
    available_speakers: List[str]

# Utility functions
def cleanup_old_files():
    """Clean up old output files"""
    try:
        if os.path.exists(output_dir):
            for file in os.listdir(output_dir):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    # Remove files older than 1 hour
                    if os.path.getmtime(file_path) < (time.time() - 3600):
                        os.remove(file_path)
    except Exception as e:
        print(f"Error cleaning up files: {e}")

def save_uploaded_file(upload_file: UploadFile) -> str:
    """Save uploaded file and return path"""
    file_extension = os.path.splitext(upload_file.filename)[1]
    temp_filename = f"{uuid.uuid4()}{file_extension}"
    temp_path = os.path.join(output_dir, temp_filename)
    
    with open(temp_path, "wb") as buffer:
        content = upload_file.file.read()
        buffer.write(content)
    
    return temp_path


# API Endpoints

@app.get("/", response_model=StatusResponse)
async def root():
    """Get API status and information"""
    available_speakers = []
    if 'en_base_speaker_tts' in models:
        available_speakers.extend(['default', 'style'])
    if 'zh_base_speaker_tts' in models:
        available_speakers.extend(['zh_default'])
    
    return StatusResponse(
        status="running",
        message="OpenVoice API is running",
        device=device,
        available_speakers=available_speakers
    )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "models_loaded": len(models) > 0}

@app.post("/tts")
async def text_to_speech(
    request: TTSRequest,
    background_tasks: BackgroundTasks
):
    """Generate speech from text using base speakers"""
    try:
        if 'en_base_speaker_tts' not in models:
            raise HTTPException(status_code=503, detail="TTS model not loaded")
        
        # Generate unique filename
        output_filename = f"tts_{uuid.uuid4()}.wav"
        output_path = os.path.join(output_dir, output_filename)
        
        # Select appropriate model and speaker
        if request.language.lower() in ['chinese', 'zh'] and 'zh_base_speaker_tts' in models:
            tts_model = models['zh_base_speaker_tts']
        else:
            tts_model = models['en_base_speaker_tts']
        
        # Generate speech
        tts_model.tts(
            text=request.text,
            output_path=output_path,
            speaker=request.speaker,
            language=request.language,
            speed=request.speed
        )
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_old_files)
        
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={"Content-Disposition": f"attachment; filename={output_filename}"}
        )
        
    except Exception as e:
        print(f"TTS Error: {e}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

@app.post("/extract_speaker_embedding")
async def extract_speaker_embedding(
    audio_file: UploadFile = File(...),
    background_tasks: BackgroundTasks = None
):
    """Extract speaker embedding from uploaded audio file"""
    try:
        if 'tone_color_converter' not in models:
            raise HTTPException(status_code=503, detail="Tone color converter not loaded")
        
        # Save uploaded file
        temp_audio_path = save_uploaded_file(audio_file)
        
        # Extract speaker embedding
        speaker_embedding = models['tone_color_converter'].extract_se([temp_audio_path])
        
        # Save speaker embedding
        embedding_filename = f"speaker_embedding_{uuid.uuid4()}.pth"
        embedding_path = os.path.join(output_dir, embedding_filename)
        torch.save(speaker_embedding.cpu(), embedding_path)
        
        # Clean up temp audio file
        os.remove(temp_audio_path)
        
        # Schedule cleanup
        if background_tasks:
            background_tasks.add_task(cleanup_old_files)
        
        return {
            "message": "Speaker embedding extracted successfully",
            "embedding_id": embedding_filename.replace('.pth', '').replace('speaker_embedding_', ''),
            "embedding_file": embedding_filename
        }
        
    except Exception as e:
        print(f"Speaker embedding extraction error: {e}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Speaker embedding extraction failed: {str(e)}")

@app.post("/voice_clone")
async def voice_clone(
    text: str = Form(...),
    language: str = Form("English"),
    speed: float = Form(1.0),
    tau: float = Form(0.3),
    reference_audio: UploadFile = File(...),
    background_tasks: BackgroundTasks = None
):
    """Clone voice from reference audio and generate speech"""
    try:
        if 'en_base_speaker_tts' not in models or 'tone_color_converter' not in models:
            raise HTTPException(status_code=503, detail="Required models not loaded")
        
        # Save reference audio
        ref_audio_path = save_uploaded_file(reference_audio)
        
        # Generate base speech
        temp_tts_filename = f"temp_tts_{uuid.uuid4()}.wav"
        temp_tts_path = os.path.join(output_dir, temp_tts_filename)
        
        # Select appropriate TTS model
        if language.lower() in ['chinese', 'zh'] and 'zh_base_speaker_tts' in models:
            tts_model = models['zh_base_speaker_tts']
            source_se = models['zh_source_se']
        else:
            tts_model = models['en_base_speaker_tts']
            source_se = models['en_source_default_se']
        
        # Generate base speech
        tts_model.tts(
            text=text,
            output_path=temp_tts_path,
            speaker="default",
            language=language,
            speed=speed
        )
        
        # Extract target speaker embedding
        target_se = models['tone_color_converter'].extract_se([ref_audio_path])
        
        # Convert voice
        output_filename = f"voice_clone_{uuid.uuid4()}.wav"
        output_path = os.path.join(output_dir, output_filename)
        
        models['tone_color_converter'].convert(
            audio_src_path=temp_tts_path,
            src_se=source_se,
            tgt_se=target_se,
            output_path=output_path,
            tau=tau
        )
        
        # Clean up temporary files
        os.remove(ref_audio_path)
        os.remove(temp_tts_path)
        
        # Schedule cleanup
        if background_tasks:
            background_tasks.add_task(cleanup_old_files)
        
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={"Content-Disposition": f"attachment; filename={output_filename}"}
        )
        
    except Exception as e:
        print(f"Voice cloning error: {e}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    import time
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
