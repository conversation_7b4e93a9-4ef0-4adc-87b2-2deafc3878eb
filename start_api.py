#!/usr/bin/env python3
"""
Startup script for OpenVoice API Server

This script helps you start the OpenVoice API server with proper configuration.
"""

import os
import sys
import subprocess
import argparse

def check_requirements():
    """Check if required packages are installed"""
    print("Checking requirements...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'torch',
        'librosa',
        'soundfile'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False
    
    return True

def check_checkpoints():
    """Check if model checkpoints are available"""
    print("\nChecking model checkpoints...")
    
    required_paths = [
        'checkpoints/base_speakers/EN/config.json',
        'checkpoints/base_speakers/EN/checkpoint.pth',
        'checkpoints/base_speakers/EN/en_default_se.pth',
        'checkpoints/base_speakers/EN/en_style_se.pth',
        'checkpoints/converter/config.json',
        'checkpoints/converter/checkpoint.pth'
    ]
    
    missing_files = []
    
    for path in required_paths:
        if os.path.exists(path):
            print(f"✅ {path}")
        else:
            print(f"❌ {path}")
            missing_files.append(path)
    
    # Check optional Chinese models
    zh_paths = [
        'checkpoints/base_speakers/ZH/config.json',
        'checkpoints/base_speakers/ZH/checkpoint.pth',
        'checkpoints/base_speakers/ZH/zh_default_se.pth'
    ]
    
    zh_available = all(os.path.exists(path) for path in zh_paths)
    if zh_available:
        print("✅ Chinese models available")
    else:
        print("⚠️  Chinese models not available (optional)")
    
    if missing_files:
        print(f"\n❌ Missing required checkpoint files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease download checkpoints:")
        print("For V1: https://myshell-public-repo-host.s3.amazonaws.com/openvoice/checkpoints_1226.zip")
        print("For V2: https://myshell-public-repo-host.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip")
        print("Extract to the checkpoints/ directory")
        return False
    
    return True

def start_server(host="0.0.0.0", port=8000, reload=False):
    """Start the FastAPI server"""
    print(f"\nStarting OpenVoice API server on {host}:{port}")
    
    cmd = [
        "uvicorn",
        "main:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")

def main():
    parser = argparse.ArgumentParser(description="Start OpenVoice API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to (default: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to (default: 8000)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    parser.add_argument("--skip-checks", action="store_true", help="Skip requirement and checkpoint checks")
    
    args = parser.parse_args()
    
    print("OpenVoice API Server Startup")
    print("=" * 40)
    
    if not args.skip_checks:
        # Check requirements
        if not check_requirements():
            sys.exit(1)
        
        # Check checkpoints
        if not check_checkpoints():
            print("\n⚠️  Some checkpoints are missing, but the server will start in limited mode.")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                sys.exit(1)
    
    # Start server
    start_server(args.host, args.port, args.reload)

if __name__ == "__main__":
    main()
