# OpenVoice API

A REST API server for OpenVoice text-to-speech and voice cloning functionality.

## Features

- **Text-to-Speech (TTS)**: Generate speech from text using pre-trained speakers
- **Voice Cloning**: Clone any voice from a reference audio file
- **Speaker Embedding Extraction**: Extract speaker characteristics from audio
- **Multi-language Support**: English and Chinese (if models are available)
- **RESTful API**: Easy to integrate with any application
- **File Management**: Automatic cleanup of temporary files

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Download Model Checkpoints

Download the required model checkpoints:

**For OpenVoice V1:**
```bash
wget https://myshell-public-repo-host.s3.amazonaws.com/openvoice/checkpoints_1226.zip
unzip checkpoints_1226.zip
```

**For OpenVoice V2:**
```bash
wget https://myshell-public-repo-host.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip
unzip checkpoints_v2_0417.zip -d checkpoints_v2
```

### 3. Start the API Server

```bash
python start_api.py
```

Or directly with uvicorn:
```bash
uvicorn openvoice_api_server:app --host 0.0.0.0 --port 8000
```

### 4. Test the API

```bash
python test_api.py
```

## API Endpoints

### Health Check

**GET** `/health`

Returns the health status of the API server.

**Response:**
```json
{
  "status": "healthy",
  "models_loaded": true
}
```

### Status Information

**GET** `/`

Returns detailed status information including available speakers and device information.

**Response:**
```json
{
  "status": "running",
  "message": "OpenVoice API is running",
  "device": "cuda:0",
  "available_speakers": ["default", "style"]
}
```

### Text-to-Speech

**POST** `/tts`

Generate speech from text using base speakers.

**Request Body:**
```json
{
  "text": "Hello, this is a test of OpenVoice!",
  "speaker": "default",
  "language": "English",
  "speed": 1.0
}
```

**Parameters:**
- `text` (string): Text to convert to speech
- `speaker` (string, optional): Speaker to use ("default", "style", "zh_default")
- `language` (string, optional): Language ("English", "Chinese")
- `speed` (float, optional): Speech speed (default: 1.0)

**Response:** Audio file (WAV format)

### Extract Speaker Embedding

**POST** `/extract_speaker_embedding`

Extract speaker characteristics from an audio file.

**Request:** Multipart form with audio file

**Response:**
```json
{
  "message": "Speaker embedding extracted successfully",
  "embedding_id": "unique-id",
  "embedding_file": "speaker_embedding_unique-id.pth"
}
```

### Voice Cloning

**POST** `/voice_clone`

Clone a voice from reference audio and generate speech.

**Request:** Multipart form with:
- `text` (string): Text to convert to speech
- `language` (string, optional): Language ("English", "Chinese")
- `speed` (float, optional): Speech speed (default: 1.0)
- `tau` (float, optional): Voice conversion strength (default: 0.3)
- `reference_audio` (file): Reference audio file

**Response:** Audio file (WAV format) with cloned voice

## Usage Examples

### Python Example

```python
import requests

# Text-to-Speech
tts_data = {
    "text": "Hello, this is OpenVoice!",
    "speaker": "default",
    "language": "English"
}
response = requests.post("http://localhost:8000/tts", json=tts_data)
with open("output.wav", "wb") as f:
    f.write(response.content)

# Voice Cloning
clone_data = {
    "text": "This is a cloned voice!",
    "language": "English"
}
with open("reference.wav", "rb") as f:
    files = {"reference_audio": f}
    response = requests.post("http://localhost:8000/voice_clone", 
                           data=clone_data, files=files)
with open("cloned_output.wav", "wb") as f:
    f.write(response.content)
```

### cURL Examples

```bash
# Text-to-Speech
curl -X POST "http://localhost:8000/tts" \
     -H "Content-Type: application/json" \
     -d '{"text": "Hello OpenVoice!", "speaker": "default"}' \
     --output output.wav

# Voice Cloning
curl -X POST "http://localhost:8000/voice_clone" \
     -F "text=This is a test of voice cloning" \
     -F "language=English" \
     -F "reference_audio=@reference.wav" \
     --output cloned.wav
```

## Configuration

### Environment Variables

- `OPENVOICE_DEVICE`: Device to use ("cuda:0", "cpu")
- `OPENVOICE_OUTPUT_DIR`: Directory for output files (default: "api_outputs")

### Command Line Options

```bash
python start_api.py --help
```

Options:
- `--host`: Host to bind to (default: 0.0.0.0)
- `--port`: Port to bind to (default: 8000)
- `--reload`: Enable auto-reload for development
- `--skip-checks`: Skip requirement and checkpoint checks

## Troubleshooting

### Common Issues

1. **Models not loading**: Ensure checkpoints are downloaded and extracted correctly
2. **CUDA out of memory**: Use CPU device or reduce batch size
3. **Audio format issues**: Ensure reference audio is in supported format (WAV, MP3)

### Logs

The server provides detailed logging for debugging:
- Model loading status
- Request processing
- Error messages with stack traces

## Development

### Running in Development Mode

```bash
python start_api.py --reload
```

### Testing

```bash
python test_api.py
```

### API Documentation

Once the server is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## License

This API wrapper follows the same license as OpenVoice (MIT License).
